import {Flex, Spin} from 'antd';
import styled from '@emotion/styled';
import {Elements} from '@/components/Chat/Element';
import {ChatMessage} from '@/types/staff/chat';
import {
    UI_DIMENSIONS,
    UI_COLORS,
    CONFIG_VALUES,
} from '../constants';
import {
    ThinkingIndicator,
    EstimatedTime,
    DotLoading,
} from './styles';

const UserContainer = styled.div`
    gap: ${UI_DIMENSIONS.SPACING_SMALL}px;
    padding: ${UI_DIMENSIONS.PADDING_XS};
    background: ${UI_COLORS.BACKGROUND_USER_BUBBLE};
    border-radius: ${UI_DIMENSIONS.BORDER_RADIUS_MEDIUM}px;
    width: fit-content;
    max-width: 70%;
`;

const AIContainer = styled.div`
    width: fit-content;
    max-width: 70%;
    margin-left: 52px;
`;
interface MessageBubbleContentProps {
    message: ChatMessage;
    isUser: boolean;
}

const MessageBubbleContent = ({message, isUser}: MessageBubbleContentProps) => {
    const isStreaming = message.stream && !message.finish;
    const isEmpty = !message?.elements?.length && isStreaming;

    if (isUser) {
        return (
            <Flex justify="end">
                <UserContainer>
                    <Elements items={message?.elements} />
                </UserContainer>
            </Flex>
        );
    }

    return (
        <Flex justify="start">
            <AIContainer>
                <Flex vertical gap={UI_DIMENSIONS.SPACING_LARGE}>
                    {isEmpty && (
                        <Flex align="center" gap={UI_DIMENSIONS.SPACING_MEDIUM}>
                            <ThinkingIndicator>
                                <Spin size={CONFIG_VALUES.SPIN_SIZE_SMALL} />
                                <span>思考中</span>
                                <EstimatedTime>（预计用时15-30秒）</EstimatedTime>
                                <DotLoading />
                            </ThinkingIndicator>
                        </Flex>
                    )}
                    <Elements items={message?.elements} />
                </Flex>
            </AIContainer>
        </Flex>
    );
};

export default MessageBubbleContent;
