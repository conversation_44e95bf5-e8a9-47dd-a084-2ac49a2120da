import {useCallback} from 'react';
import {Tooltip} from 'antd';
import {Button} from '@panda-design/components';
import CopyToClipboard from 'react-copy-to-clipboard';
import {useTransitionState} from 'huse';
import {IconCopy} from '@/icons/mcp';
import {CopyButtonWrapper} from './styles';
import {
    UI_DIMENSIONS,
    COMPONENT_CONFIGS,
    ANIMATION_VALUES,
} from './constants';

interface CopyButtonProps {
    text: string;
}

const CopyButton = ({text}: CopyButtonProps) => {
    const [noticing, setNoticing] = useTransitionState(false, ANIMATION_VALUES.COPY_NOTIFICATION_DURATION);
    const copy = useCallback(
        () => setNoticing(true),
        [setNoticing]
    );

    return (
        <CopyButtonWrapper>
            <Tooltip title={noticing ? '复制成功' : '复制'}>
                <CopyToClipboard text={text} onCopy={copy}>
                    <Button
                        icon={<IconCopy style={{fontSize: UI_DIMENSIONS.FONT_SIZE_LARGE}} />}
                        style={{
                            fontWeight: COMPONENT_CONFIGS.SUBTITLE_FONT_WEIGHT,
                            fontSize: UI_DIMENSIONS.TITLE_FONT_SIZE,
                            lineHeight: UI_DIMENSIONS.TITLE_LINE_HEIGHT,
                            border: 'none',
                        }}
                    >
                        复制
                    </Button>
                </CopyToClipboard>
            </Tooltip>
        </CopyButtonWrapper>
    );
};

export default CopyButton;
